"""
Evaluation Stack Utilities - Reusable Functions for Neural Model Evaluation

This module contains reusable functions extracted from model evaluation scripts
to support unified evaluation pipelines across different models and datasets.

Functions include:
- Dataset loading and preparation
- Model evaluation and BPS calculation  
- Stimulus-specific analyses (FixRSVP, saccades)
- QC data loading and processing
- Noise-corrected correlation calculations

Author: Extracted from model_load_eval_stack_multidataset.py
"""

import os
import json
import contextlib
from pathlib import Path
from pprint import pprint

import numpy as np
import torch
import yaml
from tqdm import tqdm

from DataYatesV1.utils.data import prepare_data
from DataYatesV1.utils.data.loading import remove_pixel_norm
from DataYatesV1.models.losses import PoissonBPSAggregator
from DataYatesV1 import get_session


def load_single_dataset(model, dataset_idx):
    """
    Load a single dataset for evaluation.
    
    Parameters
    ----------
    model : MultiDatasetModel
        The trained model containing dataset configurations
    dataset_idx : int
        Index of the dataset to load
        
    Returns
    -------
    tuple
        (train_dset, val_dset, dataset_config)
    """
    if dataset_idx >= len(model.names):
        raise ValueError(f"Dataset index {dataset_idx} out of range. Model has {len(model.names)} datasets.")
    
    if hasattr(model, 'dataset_configs'):
        dataset_config = model.dataset_configs[dataset_idx].copy()
    else:
        # combine dataset config path with dataset name + yaml extension
        config_path = model.hparams.cfg_dir
        dataset_name = model.names[dataset_idx]
        dataset_config_path = Path(config_path) / f"{dataset_name}.yaml"
        with open(dataset_config_path, 'r') as f:
            dataset_config = yaml.safe_load(f)

    dataset_name = model.names[dataset_idx]
    
    print(f"\nLoading dataset {dataset_idx}: {dataset_name}")
    # add datafilters if missing
    # dataset_config['datafilters'] = {'dfs': {'ops': [{'valid_nlags': {'n_lags': 32}}, {'missing_pct': {'theshold': 45}}], 'expose_as': 'dfs'}}
    
    # Add additional dataset types for evaluation
    if 'gratings' not in dataset_config['types']:
        dataset_config['types'] += ['gratings']
    if 'fixrsvp' not in dataset_config['types']:
        dataset_config['types'] += ['fixrsvp']
    dataset_config['keys_lags']['eyepos'] = 0
    
    # Load data with suppressed output
    with open(os.devnull, "w") as devnull, contextlib.redirect_stdout(devnull), contextlib.redirect_stderr(devnull):
        train_dset, val_dset, dataset_config = prepare_data(dataset_config, strict=False)
    
    print(f"✓ Dataset loaded: {len(train_dset)} train, {len(val_dset)} val samples")
    print(f"  Dataset config: {len(dataset_config.get('cids', []))} units")
    
    return train_dset, val_dset, dataset_config


def get_stim_inds(stim_type, train_data, val_data):
    """
    Get stimulus indices for different stimulus types.
    
    Parameters
    ----------
    stim_type : str
        Type of stimulus ('gaborium', 'backimage', 'fixrsvp', 'gratings')
    train_data : CombinedEmbeddedDataset
        Training dataset
    val_data : CombinedEmbeddedDataset  
        Validation dataset
        
    Returns
    -------
    torch.Tensor
        Indices for the specified stimulus type
    """
    if stim_type == 'gaborium':
        return train_data.get_dataset_inds('gaborium') # TODO: FIX THIS TO VAL_DATA
    elif stim_type == 'backimage':
        return val_data.get_dataset_inds('backimage')
    elif stim_type == 'fixrsvp':
        return torch.concatenate([
            train_data.get_dataset_inds('fixrsvp'),
            val_data.get_dataset_inds('fixrsvp')
        ], dim=0)
    elif stim_type == 'gratings':
        return torch.concatenate([
            train_data.get_dataset_inds('gratings'),
            val_data.get_dataset_inds('gratings')
        ], dim=0)
    else:
        raise ValueError(f"Unknown stim type: {stim_type}")


def run_model(model, batch, dataset_idx):
    """
    Run the model on a batch of data.
    
    Parameters
    ----------
    model : MultiDatasetModel
        The trained model
    batch : dict
        Batch of data with 'stim', 'behavior', etc.
    dataset_idx : int
        Index of the dataset
        
    Returns
    -------
    dict
        Batch with added 'rhat' predictions
    """
    batch = {k: v.to(model.device) for k, v in batch.items()}
    
    with torch.no_grad():
        output = model.model(batch['stim'], dataset_idx, batch.get('behavior'))
    batch['rhat'] = output
    return batch


def evaluate_dataset(model, dataset, indices, dataset_idx, batch_size=256, desc="Dataset"):
    """
    Evaluate model on a dataset and calculate bits per spike.

    Parameters
    ----------
    model : torch.nn.Module
        The model to evaluate.
    dataset : CombinedEmbeddedDataset
        The dataset to evaluate on.
    indices : torch.Tensor
        The indices to use for evaluation.
    dataset_idx : int
        Index of the dataset for the model
    batch_size : int, optional
        Batch size for evaluation, by default 256.
    desc : str, optional
        Description for progress bar, by default "Dataset".

    Returns
    -------
    tuple
        (robs, rhat, bps) - observed responses, predicted responses, bits per spike
    """
    bps_aggregator = PoissonBPSAggregator()
    dataset = dataset.shallow_copy()
    dataset.inds = indices
    robs = []
    rhat = []

    with torch.no_grad():
        for iB in tqdm(range(0, len(dataset), batch_size), desc=desc):
            batch = dataset[iB:iB+batch_size]
            batch = run_model(model, batch, dataset_idx)
            if model.log_input:
                batch['rhat'] = torch.exp(batch['rhat'])

            robs.append(batch['robs'].detach().cpu())  # Move to CPU immediately
            rhat.append(batch['rhat'].detach().cpu())  # Move to CPU immediately
            bps_aggregator(batch)

            # Clean up batch tensors to free GPU memory
            del batch
            torch.cuda.empty_cache()

    robs = torch.cat(robs, dim=0)
    rhat = torch.cat(rhat, dim=0)

    bps = bps_aggregator.closure().cpu().numpy()
    bps_aggregator.reset()

    return robs, rhat, bps


def load_qc_data(sess, cids):
    """
    Load quality control data for specified cell IDs.
    
    Parameters
    ----------
    sess : YatesV1Session
        Session object
    cids : array-like
        Cell IDs to load QC data for
        
    Returns
    -------
    dict
        Dictionary containing QC metrics for each cell
    """
    qc_data = {}
    
    try:
        # Load refractory period violation metrics
        refractory = np.load(sess.sess_dir / 'qc' / 'refractory' / 'refractory.npz')
        min_contam_proportions = refractory['min_contam_props'][cids]
        
        # Calculate contamination percentage for each unit
        contam_pct = np.array([
            np.min(min_contam_proportions[iU]) * 100
            for iU in range(len(cids))
        ])
        qc_data['contamination'] = contam_pct
        
    except Exception as e:
        print(f"Warning: Could not load refractory QC data: {e}")
        qc_data['contamination'] = np.full(len(cids), np.nan)
    
    try:
        # Load amplitude truncation metrics
        truncation = np.load(sess.sess_dir / 'qc' / 'amp_truncation' / 'truncation.npz')
        med_missing_pct = np.array([
            np.median(truncation['mpcts'][truncation['cid'] == iC])
            for iC in cids
        ])
        qc_data['truncation'] = med_missing_pct
        
    except Exception as e:
        print(f"Warning: Could not load truncation QC data: {e}")
        qc_data['truncation'] = np.full(len(cids), np.nan)
    
    try:
        # Load waveform data
        waves_full = np.load(sess.sess_dir / 'qc' / 'waveforms' / 'waveforms.npz')
        waveforms = waves_full['waveforms'][cids]
        qc_data['waveforms'] = waveforms
        qc_data['wave_times'] = waves_full['times']
        
    except Exception as e:
        print(f"Warning: Could not load waveform data: {e}")
        qc_data['waveforms'] = np.full((len(cids), 82, 384), np.nan)  # Default shape
        qc_data['wave_times'] = np.arange(82)
    
    try:
        # Load probe geometry and depth information
        ephys_meta = sess.ephys_metadata
        probe_geom = ephys_meta['probe_geometry_um']
        
        # Load laminar boundary information
        laminar_results = np.load(sess.sess_dir / 'laminar' / 'laminar.npz')
        l4_depths = laminar_results['l4_depths']
        
        qc_data['probe_geometry'] = probe_geom
        qc_data['l4_depths'] = l4_depths
        
    except Exception as e:
        print(f"Warning: Could not load probe/laminar data: {e}")
        qc_data['probe_geometry'] = None
        qc_data['l4_depths'] = None
    
    return qc_data


def get_fixrsvp_trials(model, eval_dict, dataset_idx, train_data, val_data):
    """
    Extract trial-aligned data for FixRSVP stimuli.

    Parameters
    ----------
    model : MultiDatasetModel
        The trained model
    eval_dict : dict
        Dictionary containing evaluation results with 'fixrsvp' key
    dataset_idx : int
        Index of the dataset
    train_data : CombinedEmbeddedDataset
        Training dataset
    val_data : CombinedEmbeddedDataset
        Validation dataset

    Returns
    -------
    tuple
        (robs_trial, rhat_trial, dfs_trial) - trial-aligned responses and data flags
    """
    robs = eval_dict['fixrsvp'][0]
    rhat = eval_dict['fixrsvp'][1]
    stim_indices = get_stim_inds('fixrsvp', train_data, val_data)
    data = val_data.shallow_copy()
    data.inds = stim_indices

    dset_idx = np.unique(stim_indices[:,0]).item()
    time_inds = data.dsets[dset_idx]['psth_inds'].numpy()
    trial_inds = data.dsets[dset_idx]['trial_inds'].numpy()
    unique_trials = np.unique(trial_inds)

    n_trials = len(unique_trials)
    n_time = np.max(time_inds).item()+1
    n_units = data.dsets[dset_idx]['robs'].shape[1]
    robs_trial = np.nan*np.zeros((n_trials, n_time, n_units))
    rhat_trial = np.nan*np.zeros((n_trials, n_time, n_units))
    dfs_trial = np.nan*np.zeros((n_trials, n_time, n_units))

    for itrial in range(n_trials):
        trial_idx = np.where(trial_inds == unique_trials[itrial])[0]
        eval_inds = np.where(np.isin(stim_indices[:,1], trial_idx))[0]
        data_inds = trial_idx[np.where(np.isin(trial_idx, stim_indices[:,1]))[0]]

        assert torch.all(robs[eval_inds] == data.dsets[dset_idx]['robs'][data_inds]).item(), 'robs mismatch'

        robs_trial[itrial, time_inds[data_inds]] = robs[eval_inds]
        rhat_trial[itrial, time_inds[data_inds]] = rhat[eval_inds]
        dfs_trial[itrial, time_inds[data_inds]] = data.dsets[dset_idx]['dfs'][data_inds]

    return robs_trial, rhat_trial, dfs_trial


def ccnorm_variable_trials(R, P, D=None, *,
                           ddof=0,
                           min_trials_per_bin=20,
                           min_time_bins=20):
    """
    Noise-corrected correlation (CC_norm) that allows N_t (trial count)
    to vary across time bins.

    Parameters
    ----------
    R : (N, T, K) float
        Single-trial responses (NaNs permitted).
    P : (N, T, K) float
        Model predictions (same shape as R); NaNs ignored via mask too.
    D : (N, T, K) bool or None
        Valid-sample mask.  If None, everything not-NaN in R is valid.
    ddof : int
        Passed to variance/covariance calls (0=population, 1=sample).
    min_trials_per_bin : int
        Ignore time bins with < this many valid trials.
    min_time_bins : int
        Require at least this many bins after masking, else return NaN.

    Returns
    -------
    cc : (K,) float
        CC_norm per neuron (NaN when SP ≤ 0 or not enough data).
    """
    R = np.asarray(R, float)
    P = np.asarray(P, float)
    if D is None:
        D = ~np.isnan(R)
    else:
        D = np.asarray(D, bool)

    # Apply mask: invalid entries → NaN
    R = np.where(D, R, np.nan)
    P = np.where(D, P, np.nan)

    N, T, K = R.shape
    if N < 2:
        raise ValueError("Need at least two trials.")

    cc = np.full(K, np.nan)

    for k in range(K):
        # 1.  Trial counts per time bin
        n_valid = np.sum(~np.isnan(R[:, :, k]), axis=0)      # (T,)
        good_t  = n_valid >= min_trials_per_bin

        if np.count_nonzero(good_t) < min_time_bins:
            continue                                        # leave as NaN

        r   = R[:, good_t, k]                               # (N, T_good)
        p   = P[:, good_t, k]

        # 2.  PSTH and per-bin noise variance
        y_t     = np.nanmean(r, axis=0)                     # (T_good,)
        n_t     = n_valid[good_t]
        s2_t    = np.nanvar(r, axis=0, ddof=ddof)           # across trials

        # -- explainable signal power (★)
        var_y   = np.nanvar(y_t, ddof=ddof)
        noise_correction = np.nanmean(s2_t / n_t)
        SP      = var_y - noise_correction
        if SP <= 0 or np.isnan(SP):
            continue

        # 3.  Prediction statistics
        p_mean  = np.nanmean(p, axis=0)
        cov     = np.nanmean((y_t - y_t.mean()) *
                             (p_mean - p_mean.mean()))
        var_p   = np.nanvar(p_mean, ddof=ddof)
        if var_p == 0:
            continue

        cc[k] = cov / np.sqrt(var_p * SP)

    return cc


def get_saccade_eval(stim_type, train_data, val_data, eval_dict, saccades, win=(-10, 100)):
    """
    Perform saccade-triggered analysis for a given stimulus type.

    Parameters
    ----------
    stim_type : str
        Type of stimulus ('gaborium', 'backimage', 'fixrsvp', 'gratings')
    train_data : CombinedEmbeddedDataset
        Training dataset
    val_data : CombinedEmbeddedDataset
        Validation dataset
    eval_dict : dict
        Dictionary containing evaluation results
    saccades : list of saccade dictionaries
        Output of detect saccades
    win : tuple, optional
        Time window around saccades (start, end) in bins

    Returns
    -------
    dict
        Dictionary with saccade-triggered responses and averages
    """
    
    # get saccade times
    saccade_times = torch.tensor([s['start_time'] for s in saccades])
    
    # Convert saccade times to dataset indices
    saccade_inds = train_data.get_inds_from_times(saccade_times)

    # Get stimulus indices using the helper function
    stim_inds = get_stim_inds(stim_type, train_data, val_data)

    # Use val_data for most stimulus types, train_data for gaborium
    if stim_type == 'gaborium':
        dataset = train_data.shallow_copy()
    else:
        dataset = val_data.shallow_copy()

    dataset.inds = stim_inds

    dset = stim_inds[0,0]
    print(f'Dataset {dset}')

    robs = eval_dict[stim_type][0]
    pred = eval_dict[stim_type][1]

    # print(f"Number of robs bins: {robs.shape[0]} Number of stim bins: {stim_inds.shape[0]}")

    nbins = win[1]-win[0]

    valid_saccades = np.where(saccade_inds[:,0]==dset)[0]

    sac_indices = saccade_inds[valid_saccades, 1]
    n_sac = len(sac_indices)
    robs_sac = np.nan*np.zeros((n_sac, nbins, robs.shape[1]))
    pred_sac = np.nan*np.zeros((n_sac, nbins, pred.shape[1]))
    dfs_sac = np.nan*np.zeros((n_sac, nbins, robs.shape[1]))

    saccade_info = [saccades[i] for i in valid_saccades]

    for i,isac in enumerate(sac_indices):
        # print(f"i: {i}/{n_sac}, isac: {isac}")
        j = np.where(stim_inds[:,1] == isac)[0]

        if len(j) == 0:
            continue

        j = j.item()

        dataset_idx = np.where(torch.all(dataset.inds == stim_inds[j], 1))[0]
        if len(dataset_idx) == 0:
            continue
        dataset_idx = dataset_idx.item()

        if (j + win[0] >= 0) & (j + win[1] < robs.shape[0]):
            robs_ = robs[(j+win[0]):(j+win[1])]
            pred_ = pred[(j+win[0]):(j+win[1])]
            batch= dataset[dataset_idx+win[0]:dataset_idx+win[1]]
            assert torch.all(batch['robs'] == robs_), 'robs mismatch'
            dfs_ = batch['dfs']

            robs_sac[i] = robs_
            pred_sac[i] = pred_
            dfs_sac[i] = dfs_

    good = np.where(np.sum(np.isnan(robs_sac), axis=(1,2)) == 0)[0]

    robs_sac = robs_sac[good]
    pred_sac = pred_sac[good]
    dfs_sac = dfs_sac[good]

    saccade_info = [saccade_info[i] for i in good]

    print(f"Number of good saccades: {len(good)}")
    rbar = np.nansum(robs_sac*dfs_sac, axis=0) / np.nansum(dfs_sac, axis=0)
    rbarhat = np.nansum(pred_sac*dfs_sac, axis=0) / np.nansum(dfs_sac, axis=0)

    return {'robs': robs_sac, 'pred': pred_sac, 'dfs': dfs_sac, 'rbar': rbar, 'rbarhat': rbarhat, 'saccade_info': saccade_info, 'win': win}


def detect_saccades_from_session(sess):
    """
    Load or detect saccades from a session.

    Parameters
    ----------
    sess : YatesV1Session
        Session object

    Returns
    -------
    torch.Tensor
        Saccade times
    """
    try:
        # Try to load from JSON first
        saccades = json.load(open(sess.sess_dir / 'saccades' / 'saccades.json'))
        saccade_times = torch.tensor([s['start_time'] for s in saccades])
    except:
        # Fall back to detect_saccades if available
        try:
            from jake.detect_saccades import detect_saccades
            saccades = detect_saccades(sess)
            saccade_times = torch.sort(torch.tensor([s.start_time for s in saccades])).values
        except ImportError:
            print("Warning: Could not load or detect saccades")
            return torch.tensor([])

    # Filter saccades with minimum ISI
    valid = np.diff(saccade_times.numpy(), prepend=0) > 0.06
    
    saccades = [saccades[i] for i in np.where(valid)[0]]

    # clean up saccades based on the main sequence
    vel = np.array([s['A'] for s in saccades])
    amp = np.array([np.hypot(s['end_x']-s['start_x'], s['end_y']-s['start_y']) for s in saccades])

    rat = vel/amp
    med = np.median(rat)
    mad = np.median(np.abs(rat-med))
    inliers = np.where(np.abs(rat-med) < 3*mad)[0]
    saccades = [saccades[i] for i in inliers]

    return saccades
