#!/usr/bin/env python3
"""
Clean extraction functions for cross-model analysis.

Functions to extract BPS, saccade, CCNORM, and QC data from evaluation results.
"""

#%% Setup and Imports
import torch._dynamo
torch._dynamo.config.suppress_errors = True

import sys
from pathlib import Path
sys.path.append('.')

import numpy as np
from eval_stack_multidataset import evaluate_model_multidataset, load_single_dataset
from DataYatesV1 import get_session
from DataYatesV1 import enable_autoreload

import matplotlib.pyplot as plt

enable_autoreload()

#%% Discover Available Models
print("🔍 Discovering available models...")
from eval_stack_multidataset import scan_checkpoints
checkpoint_dir = '/mnt/ssd/YatesMarmoV1/conv_model_fits/experiments/multidataset_smooth_120/checkpoints'
models_by_type = scan_checkpoints(checkpoint_dir)

print(f"Found {len(models_by_type)} model types:")
for model_type, models in models_by_type.items():
    if models:
        best_model = models[0]
        if best_model.get('metric_type') == 'bps' and best_model.get('val_bps') is not None:
            best_metric = f"best BPS: {best_model['val_bps']:.4f}"
        else:
            best_metric = f"best loss: {best_model['val_loss']:.4f}"
        print(f"  {model_type}: {len(models)} models ({best_metric})")
    else:
        print(f"  {model_type}: 0 models")

#%%
from eval_stack_multidataset import load_model

model, model_info = load_model(
        model_type='learned_res_small',
        model_index=None,
        checkpoint_path=None,
        checkpoint_dir=checkpoint_dir,
        device='cpu'
    )

model.eval()


#%% Load Multiple Models for Comparison
print("\n📊 Loading models for comparison...")

# Define models to compare
models_to_compare = ['learned_res_small']#, 'learned_res_small_gru']#, 'learned_res_small_pc', 'learned_res_small_stn', 'learned_res_small_film']
available_models = [m for m in models_to_compare if m in models_by_type]

print(f"Comparing models: {available_models}")

# Load results for each model
all_results = {}
for model_type in available_models:
    print(f"\nLoading {model_type}...")
    
    results = evaluate_model_multidataset(
        model_type=model_type,
        analyses=['bps', 'ccnorm', 'saccade', 'sta'],  # Include all analyses including STA
        checkpoint_dir=checkpoint_dir,
        save_dir="/mnt/ssd/YatesMarmoV1/conv_model_fits/eval_stack_smooth_120",
        recalc=False,
        batch_size=64
    )
    all_results.update(results)
    model_name = list(results.keys())[0]
    n_cells = len(results[model_name]['qc']['all_cids'])
    print(f"  ✅ {model_name}: {n_cells} cells")

    # Save incrementally after each model to prevent data loss
    import pickle
    from pathlib import Path
    save_path = Path('all_results_120_analysis_incremental.pkl')
    try:
        with open(save_path, 'wb') as f:
            pickle.dump(all_results, f)
        print(f"  💾 Incremental save: {len(all_results)} models ({save_path.stat().st_size / 1024 / 1024:.1f} MB)")
    except Exception as e:
        print(f"  ⚠️ Incremental save failed: {e}")

print(f"\n✅ Loaded {len(all_results)} models for comparison")

#%%

from gratings_analysis import gratings_analysis, plot_gratings_results, plot_gratings_comparison, gratings_comparison

gratings_results = []
for dset_idx in range(20):
    train_data, val_data, dataset_config = load_single_dataset(model, dset_idx)

    try:
        gratings_ind = int(np.where([d.metadata['name'] == 'gratings' for d in train_data.dsets])[0])
    except:
        print(f'No gratings dataset for {model.names[dset_idx]}')
        continue

    # Pull out the gratings dataset
    gratings_dset = train_data.dsets[gratings_ind]
    train_inds = train_data.inds[train_data.inds[:,0] == gratings_ind][:,1]
    val_inds = val_data.inds[val_data.inds[:,0] == gratings_ind][:,1]
    #inds = np.sort(np.concatenate((train_inds, val_inds)))
    inds = val_inds
    print(len(val_inds))
    n_lags = dataset_config['keys_lags']['stim'][-1]
    robs = gratings_dset['robs'][inds].numpy()
    rhat = results[list(results.keys())[0]]['bps']['gratings']['rhat'][dset_idx].numpy()
    print(f'R_obs: {robs.shape}')
    print(f'R_hat: {rhat.shape}')
    sf = gratings_dset['sf'][inds]
    ori = gratings_dset['ori'][inds]
    phases = gratings_dset['stim_phase'][inds]
    phases = phases[:,phases.shape[1]//2, phases.shape[2]//2]
    dt = 1/dataset_config['sampling']['target_rate']
    dfs = gratings_dset['dfs'].numpy().squeeze()[inds]

    r = gratings_comparison(
        robs=robs,
        rhat=rhat,
        sf=sf,
        ori=ori,
        phases=phases,
        dt=dt,
        n_lags=n_lags,
        dfs=dfs,
        min_spikes = 30,
        #inds = inds
    )
    gratings_results.append(r)

    print('\n------------------------------------\n')
#%%
robs_modulation_index = []
rhat_modulation_index = []

for r in gratings_results:
    for sine_fit in r['robs']['sine_fit_results']:
        if sine_fit is not None:
            robs_modulation_index.append(sine_fit['modulation_index'])
        else:
            robs_modulation_index.append(np.nan)
    for sine_fit in r['rhat']['sine_fit_results']:
        if sine_fit is not None:
            rhat_modulation_index.append(sine_fit['modulation_index'])
        else:
            rhat_modulation_index.append(np.nan)
robs_modulation_index = np.array(robs_modulation_index)
rhat_modulation_index = np.array(rhat_modulation_index)
n_units = np.sum(~np.isnan(robs_modulation_index) & ~np.isnan(rhat_modulation_index))

plt.figure()
plt.plot(robs_modulation_index, rhat_modulation_index, 'C0.')
plt.plot(plt.xlim(), plt.xlim(), 'k--', alpha=.5)
plt.xlabel('Data Modulation Index')
plt.ylabel('Model Modulation Index')
plt.title(f'Model: {list(results.keys())[0]}\n Units: {n_units}')
plt.show()
#%%

from tqdm import tqdm
from matplotlib.backends.backend_pdf import PdfPages
pdf_name = 'gratings_results.pdf'
n_units_total = np.sum([d['robs']['n_units'] for d in gratings_results])

with PdfPages(pdf_name) as pdf:
    with tqdm(total=n_units_total, desc='Plotting Gratings Results') as pbar:
        for id, d in enumerate(gratings_results):
            for iU in range(d['robs']['n_units']):
                fig, axs = plot_gratings_comparison(d, iU)
                t = axs[0].title.get_text()
                axs[0].set_title(f'Dataset {id} - {t}')
                pdf.savefig(fig)
                plt.close(fig)
                pbar.update(1)


